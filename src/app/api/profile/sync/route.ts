import { NextResponse } from "next/server";
import { createSupabaseServerClient } from "@/lib/supabase/server";
import { createDrizzle } from "@/config/database";

// This endpoint syncs user profile data with auth data
export async function POST() {
  const supabase = await createSupabaseServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { client } = createDrizzle();
  await client.connect();
  
  try {
    // Check if profile exists
    const { rows: existingProfile } = await client.query(
      `SELECT id FROM profiles WHERE id = $1`,
      [user.id]
    );

    if (existingProfile.length === 0) {
      // Create profile with email
      await client.query(
        `INSERT INTO profiles (id, email, display_name) VALUES ($1, $2, $3)`,
        [user.id, user.email, user.email?.split('@')[0] || 'User']
      );
    } else {
      // Update profile with email if missing
      await client.query(
        `UPDATE profiles SET email = $2, display_name = COALESCE(display_name, $3) WHERE id = $1 AND email IS NULL`,
        [user.id, user.email, user.email?.split('@')[0] || 'User']
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Profile sync error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  } finally {
    await client.end();
  }
}
