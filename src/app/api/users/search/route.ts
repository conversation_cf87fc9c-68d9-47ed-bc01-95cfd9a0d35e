import { NextRequest, NextResponse } from "next/server";
import { createSupabaseServerClient } from "@/lib/supabase/server";
import { createDrizzle } from "@/config/database";

export async function GET(req: NextRequest) {
  const supabase = await createSupabaseServerClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  
  if (!user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { searchParams } = new URL(req.url);
  const email = searchParams.get("email");
  
  if (!email || email.length < 3) {
    return NextResponse.json({ error: "Email query must be at least 3 characters" }, { status: 400 });
  }

  const { client } = createDrizzle();
  await client.connect();
  
  try {
    // For now, we'll return a simple message since we need to set up proper user search
    // In a production app, you'd want to either:
    // 1. Store emails in profiles table during signup
    // 2. Use Supabase admin API with proper service role key
    // 3. Create a server-side function to search users

    return NextResponse.json({
      users: [],
      message: "User search will be available once profiles are populated with emails"
    });
  } catch (error) {
    console.error("Search error:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  } finally {
    await client.end();
  }
}
