"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Users, UserMinus, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";

interface User {
  id: string;
  email: string;
  displayName: string;
  followedAt: string;
}

interface FollowingListProps {
  type?: "following" | "followers";
  userId?: string;
  title?: string;
}

export default function FollowingList({ 
  type = "following", 
  userId, 
  title 
}: FollowingListProps) {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [unfollowingIds, setUnfollowingIds] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchUsers();
  }, [type, userId]);

  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({ type });
      if (userId) params.append("userId", userId);
      
      const response = await fetch(`/api/follows?${params}`);
      const data = await response.json();

      if (response.ok) {
        setUsers(data.users);
      } else {
        toast.error(data.error || "Failed to load users");
      }
    } catch (error) {
      console.error("Fetch users error:", error);
      toast.error("Failed to load users");
    } finally {
      setIsLoading(false);
    }
  };

  const handleUnfollow = async (followingId: string) => {
    setUnfollowingIds(prev => new Set(prev).add(followingId));

    try {
      const response = await fetch(`/api/follows?followingId=${followingId}`, {
        method: "DELETE",
      });

      const data = await response.json();

      if (response.ok) {
        setUsers(prev => prev.filter(user => user.id !== followingId));
        toast.success("User unfollowed successfully!");
      } else {
        toast.error(data.error || "Failed to unfollow user");
      }
    } catch (error) {
      console.error("Unfollow error:", error);
      toast.error("Failed to unfollow user");
    } finally {
      setUnfollowingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(followingId);
        return newSet;
      });
    }
  };

  const displayTitle = title || (type === "following" ? "Following" : "Followers");

  return (
    <Card className="overflow-hidden bg-[linear-gradient(to_bottom,_#f7d488_0%,_#fbfbea_22%,_#e7f7ee_38%,_#a8d5c8_60%,_#2a4a5e_86%,_#193043_100%)] shadow-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          {displayTitle}
          {!isLoading && <span className="text-sm font-normal">({users.length})</span>}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <AnimatePresence mode="wait">
          {isLoading ? (
            <motion.div
              key="loading"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="flex items-center justify-center py-8"
            >
              <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            </motion.div>
          ) : users.length > 0 ? (
            <motion.div
              key="users"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="space-y-3"
            >
              {users.map((user, index) => (
                <motion.div
                  key={user.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-3 rounded-lg bg-white/20 dark:bg-black/20 backdrop-blur-sm"
                >
                  <div className="flex-1">
                    <h3 className="font-medium text-sm">{user.displayName}</h3>
                    <p className="text-xs text-muted-foreground">{user.email}</p>
                    <p className="text-xs text-muted-foreground">
                      {type === "following" ? "Following since" : "Follower since"} {new Date(user.followedAt).toLocaleDateString()}
                    </p>
                  </div>
                  {type === "following" && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleUnfollow(user.id)}
                      disabled={unfollowingIds.has(user.id)}
                      className="ml-4 rounded-full bg-white/50 hover:bg-white/70 dark:bg-black/50 dark:hover:bg-black/70"
                    >
                      {unfollowingIds.has(user.id) ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <>
                          <UserMinus className="h-4 w-4 mr-1" />
                          Unfollow
                        </>
                      )}
                    </Button>
                  )}
                </motion.div>
              ))}
            </motion.div>
          ) : (
            <motion.div
              key="empty"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="text-center py-8 text-muted-foreground"
            >
              <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>
                {type === "following" 
                  ? "Not following anyone yet" 
                  : "No followers yet"
                }
              </p>
            </motion.div>
          )}
        </AnimatePresence>
      </CardContent>
    </Card>
  );
}
