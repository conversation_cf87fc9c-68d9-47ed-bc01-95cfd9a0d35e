"use client";

import { useState, useEffect, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Search, UserPlus, User<PERSON>heck, Loader2 } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { toast } from "sonner";
// Simple debounce implementation
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): T & { cancel: () => void } {
  let timeout: NodeJS.Timeout | null = null;

  const debounced = ((...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  }) as T & { cancel: () => void };

  debounced.cancel = () => {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
  };

  return debounced;
}

interface User {
  id: string;
  email: string;
  displayName: string;
}

interface UserWithFollowStatus extends User {
  isFollowing?: boolean;
  isLoading?: boolean;
}

export default function SearchUsers() {
  const [searchQuery, setSearchQuery] = useState("");
  const [users, setUsers] = useState<UserWithFollowStatus[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(async (query: string) => {
      if (query.length < 3) {
        setUsers([]);
        setHasSearched(false);
        return;
      }

      setIsSearching(true);
      try {
        const response = await fetch(`/api/users/search?email=${encodeURIComponent(query)}`);
        const data = await response.json();

        if (response.ok) {
          // Get follow status for each user
          const usersWithStatus = await Promise.all(
            data.users.map(async (user: User) => {
              try {
                const statusResponse = await fetch(`/api/follows/status?userId=${user.id}`);
                const statusData = await statusResponse.json();
                return {
                  ...user,
                  isFollowing: statusData.isFollowing || false,
                  isLoading: false,
                };
              } catch {
                return { ...user, isFollowing: false, isLoading: false };
              }
            })
          );
          setUsers(usersWithStatus);
        } else {
          toast.error(data.error || "Search failed");
          setUsers([]);
        }
      } catch (error) {
        console.error("Search error:", error);
        toast.error("Search failed");
        setUsers([]);
      } finally {
        setIsSearching(false);
        setHasSearched(true);
      }
    }, 500),
    []
  );

  useEffect(() => {
    debouncedSearch(searchQuery);
    return () => {
      debouncedSearch.cancel();
    };
  }, [searchQuery, debouncedSearch]);

  const handleFollow = async (userId: string) => {
    setUsers(prev => 
      prev.map(user => 
        user.id === userId ? { ...user, isLoading: true } : user
      )
    );

    try {
      const response = await fetch("/api/follows", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ followingId: userId }),
      });

      const data = await response.json();

      if (response.ok) {
        setUsers(prev => 
          prev.map(user => 
            user.id === userId 
              ? { ...user, isFollowing: true, isLoading: false }
              : user
          )
        );
        toast.success("User followed successfully!");
      } else {
        toast.error(data.error || "Failed to follow user");
        setUsers(prev => 
          prev.map(user => 
            user.id === userId ? { ...user, isLoading: false } : user
          )
        );
      }
    } catch (error) {
      console.error("Follow error:", error);
      toast.error("Failed to follow user");
      setUsers(prev => 
        prev.map(user => 
          user.id === userId ? { ...user, isLoading: false } : user
        )
      );
    }
  };

  const handleUnfollow = async (userId: string) => {
    setUsers(prev => 
      prev.map(user => 
        user.id === userId ? { ...user, isLoading: true } : user
      )
    );

    try {
      const response = await fetch(`/api/follows?followingId=${userId}`, {
        method: "DELETE",
      });

      const data = await response.json();

      if (response.ok) {
        setUsers(prev => 
          prev.map(user => 
            user.id === userId 
              ? { ...user, isFollowing: false, isLoading: false }
              : user
          )
        );
        toast.success("User unfollowed successfully!");
      } else {
        toast.error(data.error || "Failed to unfollow user");
        setUsers(prev => 
          prev.map(user => 
            user.id === userId ? { ...user, isLoading: false } : user
          )
        );
      }
    } catch (error) {
      console.error("Unfollow error:", error);
      toast.error("Failed to unfollow user");
      setUsers(prev => 
        prev.map(user => 
          user.id === userId ? { ...user, isLoading: false } : user
        )
      );
    }
  };

  return (
    <div className="space-y-4">
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          type="email"
          placeholder="Search users by email..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 bg-gradient-to-tr from-white/80 via-white/60 to-white/40 border-white/40 dark:from-white/15 dark:via-white/10 dark:to-white/5 dark:border-white/10"
        />
        {isSearching && (
          <Loader2 className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 animate-spin text-muted-foreground" />
        )}
      </div>

      <AnimatePresence mode="wait">
        {isSearching ? (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex items-center justify-center py-8"
          >
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          </motion.div>
        ) : users.length > 0 ? (
          <motion.div
            key="results"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="space-y-3"
          >
            {users.map((user, index) => (
              <motion.div
                key={user.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="overflow-hidden bg-gradient-to-r from-white/80 to-white/60 dark:from-white/10 dark:to-white/5 border-white/40 dark:border-white/10">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium">{user.displayName}</h3>
                        <p className="text-sm text-muted-foreground">{user.email}</p>
                      </div>
                      <Button
                        size="sm"
                        variant={user.isFollowing ? "outline" : "default"}
                        onClick={() => user.isFollowing ? handleUnfollow(user.id) : handleFollow(user.id)}
                        disabled={user.isLoading}
                        className="ml-4 rounded-full"
                      >
                        {user.isLoading ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : user.isFollowing ? (
                          <>
                            <UserCheck className="h-4 w-4 mr-1" />
                            Following
                          </>
                        ) : (
                          <>
                            <UserPlus className="h-4 w-4 mr-1" />
                            Follow
                          </>
                        )}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        ) : hasSearched && searchQuery.length >= 3 ? (
          <motion.div
            key="no-results"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="text-center py-8 text-muted-foreground"
          >
            <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No users found for "{searchQuery}"</p>
          </motion.div>
        ) : null}
      </AnimatePresence>
    </div>
  );
}
